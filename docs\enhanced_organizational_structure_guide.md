# Enhanced Organizational Structure Guide for TTRPG Content

## Overview

This guide outlines the enhanced organizational structure incorporated into the narrative entities schema, providing a comprehensive framework for organizing TTRPG content from campaigns down to individual encounters.

## Hierarchical Organization Structure

### 1. **Campaign Level** (Top-Level)
- **Entity Type**: `campaign`
- **Purpose**: Represents the overarching campaign or story arc
- **Contains**: Multiple adventures, major NPCs, central locations
- **Example ID**: `curse-of-strahd-campaign`

### 2. **Adventure/Module Level**
- **Entity Type**: `adventure`
- **Purpose**: Individual adventure modules or story arcs within a campaign
- **Contains**: Chapters, locations, NPCs, events
- **Example ID**: `cos.death-house`

### 3. **Chapter Level**
- **Entity Type**: `chapter`
- **Purpose**: Major divisions within an adventure
- **Contains**: Adventure sections, key events
- **Example ID**: `cos.chapter-1-into-the-mists`

### 4. **Section Level**
- **Entity Type**: `adventure_section`
- **Purpose**: Specific encounters, locations, or story beats
- **Contains**: Detailed encounter information
- **Example ID**: `cos.death-house.entrance-hall`

### 5. **Supporting Entities**
- **Locations**: `location` - Physical places and environments
- **NPCs**: `npc` - Non-player characters
- **Factions**: `faction` - Organizations and groups
- **Events**: `event` - Specific occurrences or encounters
- **Quests**: `quest` - Specific objectives or missions

## Key Organizational Features

### Hierarchical Positioning
Each entity includes an `organizational_hierarchy` object that defines its position:

```json
"organizational_hierarchy": {
  "campaign_id": "curse-of-strahd-campaign",
  "module_id": "death-house",
  "chapter_id": "chapter-1-into-the-mists",
  "section_id": "section-a-village-approach",
  "subsection_id": "room-1-entrance-hall"
}
```

### Relationship Tracking
The `relationships` object defines connections between entities:

```json
"relationships": {
  "contains": ["cos.npc.strahd", "cos.location.castle-ravenloft"],
  "contained_by": "curse-of-strahd-campaign",
  "references": ["cos.item.sunsword"],
  "referenced_by": ["cos.quest.find-the-artifacts"],
  "depends_on": ["cos.event.death-house-escape"],
  "enables": ["cos.chapter-2-village-of-barovia"]
}
```

### Content Organization
The `content_organization` object provides metadata for presentation:

```json
"content_organization": {
  "display_order": 1,
  "content_type": "encounter",
  "complexity_level": "moderate",
  "estimated_duration_minutes": 45,
  "prerequisites": ["cos.event.arrival-in-barovia"]
}
```

## Practical Implementation Examples

### Campaign Structure Example
```
Curse of Strahd Campaign
├── Death House (Adventure)
│   ├── Chapter 1: Into the Mists
│   │   ├── Section A: Village Approach
│   │   │   ├── Encounter 1: Mist Encounter
│   │   │   └── Encounter 2: Village Gates
│   │   └── Section B: Death House Proper
│   │       ├── Room 1: Entrance Hall
│   │       └── Room 2: Main Hall
│   └── Chapter 2: The House's Secrets
├── Village of Barovia (Adventure)
└── Castle Ravenloft (Adventure)
```

### Entity ID Naming Convention
- **Campaign**: `campaign-slug`
- **Adventure**: `campaign-slug.adventure-slug`
- **Chapter**: `campaign-slug.adventure-slug.chapter-slug`
- **Section**: `campaign-slug.adventure-slug.section-slug`
- **Specific Entity**: `campaign-slug.entity-type.entity-slug`

Examples:
- `cos` (Curse of Strahd campaign)
- `cos.death-house` (Death House adventure)
- `cos.chapter-1-into-the-mists` (Chapter 1)
- `cos.npc.strahd-von-zarovich` (Strahd NPC)
- `cos.location.village-of-barovia` (Village location)

## Benefits of This Structure

### 1. **Clear Hierarchy**
- Easy navigation from high-level campaign down to specific encounters
- Clear parent-child relationships between content elements

### 2. **Flexible Cross-Referencing**
- Entities can reference each other regardless of hierarchical position
- Support for complex narrative dependencies and story branching

### 3. **Enhanced Searchability**
- Rich metadata enables sophisticated filtering and searching
- Content can be organized by type, complexity, duration, etc.

### 4. **AI/LLM Optimization**
- Clear relationships help AI systems understand content connections
- Hierarchical structure provides context for content generation and modification

### 5. **Modular Content Management**
- Individual entities can be easily moved, copied, or modified
- Support for content reuse across different campaigns or adventures

## Integration with Existing Schemas

This enhanced structure builds upon your existing schemas:

- **Narrative Schema**: Provides the organizational framework
- **D&D 5e Schema**: Adds mechanical details to narrative entities
- **General TTRPG Schema**: Bridges narrative and mechanical content

The organizational structure is designed to work seamlessly with your existing PDF-to-Markdown pipeline and RAG system, providing better context and relationships for content retrieval and generation.
