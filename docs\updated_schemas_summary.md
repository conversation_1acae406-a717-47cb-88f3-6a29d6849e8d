# Updated Schemas Summary: Enhanced Organizational Structure

## Overview

Both the narrative and D&D 5e schemas have been updated to incorporate the new organizational paradigm. These changes maintain full backward compatibility while adding powerful new organizational and relationship tracking capabilities.

## Key Changes Made

### 1. **Universal Organizational Fields**

All entities now include three new core objects:

#### **Organizational Hierarchy**
```json
"organizational_hierarchy": {
  "campaign_id": "string|null",      // null for standalone adventures
  "module_id": "string|null",        // adventure/module identifier
  "chapter_id": "string|null",       // chapter within adventure
  "section_id": "string|null",       // section within chapter
  "subsection_id": "string|null"     // fine-grained organization
}
```

#### **Relationships**
```json
"relationships": {
  "contains": ["array of entity IDs"],
  "contained_by": "string|null",
  "references": ["array of entity IDs"],
  "referenced_by": ["array of entity IDs"],
  "depends_on": ["array of entity IDs"],
  "enables": ["array of entity IDs"]
}
```

#### **Content Organization**
```json
"content_organization": {
  "display_order": "integer|null",
  "content_type": "enum",
  "complexity_level": "enum",
  "estimated_duration_minutes": "integer|null",  // narrative only
  "encounter_difficulty": "enum|null",           // D&D 5e only
  "prerequisites": ["array of entity IDs"]
}
```

### 2. **Expanded Entity Types**

#### **Narrative Schema New Entity Types:**
- `campaign` - Top-level campaign containers
- `chapter` - Major divisions within adventures
- `faction` - Organizations and groups
- `event` - Specific occurrences or encounters
- `quest` - Objectives and missions
- `organization` - Formal groups and institutions
- `settlement` - Towns, cities, and communities
- `region` - Geographic areas

#### **D&D 5e Schema New Entity Types:**
- `encounter_dnd5e` - Complete D&D 5e encounters
- `magic_item` - Distinct from regular items
- `vehicle` - Ships, mounts, etc.
- `disease` - Diseases and afflictions
- `madness` - Madness effects

### 3. **New Entity Definitions**

#### **Narrative Schema:**
- **`campaign_yaml`**: Complete campaign structure with themes, level ranges, contained adventures
- **`chapter_yaml`**: Chapter organization within adventures
- **`faction_yaml`**: Detailed faction/organization tracking with power levels, goals, members
- **`event_yaml`**: Events with triggers, participants, consequences
- **`quest_yaml`**: Quests with objectives, rewards, prerequisites
- **`region_yaml`**: Geographic regions with climate, settlements, factions

#### **D&D 5e Schema:**
- **`encounter_dnd5e_yaml`**: Complete encounter definitions with creatures, hazards, treasure, scaling

## Standalone Adventure Support

### Default Standalone Structure
```yaml
organizational_hierarchy:
  campaign_id: null                    # Key: null means standalone
  module_id: "adventure-slug"          # Self-referential
  chapter_id: null
  section_id: null
  subsection_id: null

relationships:
  contained_by: null                   # Key: null means no parent
  depends_on: []                       # No external dependencies
  enables: []                          # Doesn't unlock other content
```

### Campaign Integration
When incorporating into a campaign, simply update:
```yaml
organizational_hierarchy:
  campaign_id: "my-campaign"           # Now part of a campaign
  
relationships:
  contained_by: "my-campaign"          # Now contained by campaign
  references: ["campaign.npc.quest-giver"]  # Can reference campaign entities
```

## Practical Examples

### Standalone Adventure Processing
```yaml
# Input: "Tomb of the Serpent Kings.pdf"
# Output: Standalone adventure structure

id: "tomb-of-the-serpent-kings"
organizational_hierarchy:
  campaign_id: null                    # Standalone
  module_id: "tomb-of-the-serpent-kings"
relationships:
  contained_by: null                   # No parent
  contains: ["totsk.location.entrance", "totsk.npc.guardian"]
tags: ["standalone", "one-shot", "dungeon-crawl"]
```

### Campaign Integration Example
```yaml
# Later integration into campaign

organizational_hierarchy:
  campaign_id: "my-homebrew-campaign"  # Now part of campaign
relationships:
  contained_by: "my-homebrew-campaign"
  enables: ["campaign.location.hidden-valley"]  # Unlocks campaign content
```

## Benefits of Updated Schemas

### 1. **Flexible Processing**
- Process any adventure as standalone by default
- No need to know campaign context during initial processing
- Clean, self-contained entities

### 2. **Rich Relationship Tracking**
- Explicit connections between all entities
- Support for complex narrative dependencies
- Bidirectional relationship management

### 3. **Enhanced AI/LLM Support**
- Better context for content generation
- Clear hierarchical structure for understanding
- Rich metadata for improved RAG performance

### 4. **Scalable Organization**
- From simple one-shots to complex multi-campaign settings
- Modular design allows content reuse
- Clear separation of concerns

### 5. **Backward Compatibility**
- All existing functionality preserved
- New fields are optional
- Gradual migration possible

## Schema Validation

Both schemas maintain strict validation while supporting flexibility:

```json
{
  "organizational_hierarchy": {
    "campaign_id": {"type": ["string", "null"]},  // null for standalone
    "module_id": {"type": ["string", "null"]}     // required for organization
  },
  "relationships": {
    "contained_by": {"type": ["string", "null"]}  // null for top-level entities
  }
}
```

## Migration Path

1. **Existing Content**: Continues to work without modification
2. **New Processing**: Automatically includes organizational structure
3. **Gradual Enhancement**: Add relationships and organization over time
4. **Full Integration**: Leverage complete organizational capabilities

## File Organization

Recommended directory structure:
```
content/
├── campaigns/           # Campaign-level entities
├── adventures/          # Adventure/module entities  
├── chapters/           # Chapter entities
├── adventure-sections/ # Section entities
├── locations/          # Location entities
├── npcs/              # NPC entities
├── factions/          # Faction entities
├── events/            # Event entities
├── quests/            # Quest entities
├── encounters/        # D&D 5e encounter entities
└── monsters/          # D&D 5e monster entities
```

This updated schema framework provides the foundation for sophisticated TTRPG content management while maintaining the flexibility needed for your one-shot focused pipeline.
