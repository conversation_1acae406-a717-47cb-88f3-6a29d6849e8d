# Standalone Adventure Integration Guide

## Overview

This guide explains how the enhanced organizational schema handles single adventures, one-shots, and standalone modules that can later be incorporated into campaigns. The schema is designed with modularity as a core principle.

## Default Standalone Adventure Structure

### Minimal Required Fields for Standalone Adventures

```yaml
---
id: "adventure-slug"
entity_type: "adventure"
name: "adventure-slug"
game_system: "dnd5e"
adventure_slug: "adventure-slug"
organizational_hierarchy:
  campaign_id: null                    # Key: null means standalone
  module_id: "adventure-slug"          # Self-referential
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Adventure Source"
tags: ["standalone", "one-shot"]       # Tag as standalone
summary: "Brief adventure description"
relationships:
  contains: []                         # Will contain locations, NPCs, etc.
  contained_by: null                   # Key: null means no parent
  references: []                       # Empty for standalone
  referenced_by: []                    # Empty initially
  depends_on: []                       # No dependencies
  enables: []                          # Doesn't unlock other content
content_organization:
  display_order: 1
  content_type: "encounter"
  complexity_level: "simple"
  estimated_duration_minutes: 240     # Typical one-shot duration
  prerequisites: []                    # No prerequisites
# ... standard adventure fields
---
```

## Pipeline Processing for Standalone Adventures

### 1. PDF Ingestion
Your pipeline processes a standalone adventure PDF:

```
Input: "Tomb of the Serpent Kings.pdf"
↓
Conversion: Raw Markdown
↓
Processing: Structured TTRPG Markdown with schema
↓
Output: Standalone adventure with null campaign_id
```

### 2. Automatic Standalone Detection
Your processing service can automatically detect standalone adventures:

```python
def detect_standalone_adventure(content):
    """Detect if this is a standalone adventure"""
    indicators = [
        "one-shot" in content.lower(),
        "standalone" in content.lower(),
        no_campaign_references_found(content),
        single_adventure_structure(content)
    ]
    return any(indicators)

def create_standalone_hierarchy(adventure_slug):
    """Create organizational hierarchy for standalone adventure"""
    return {
        "campaign_id": None,
        "module_id": adventure_slug,
        "chapter_id": None,
        "section_id": None,
        "subsection_id": None
    }
```

### 3. Standalone Entity Creation
All entities within a standalone adventure reference only each other:

```yaml
# Adventure sections reference the adventure
parent_adventure_id: "totsk.tomb-of-the-serpent-kings"

# NPCs are contained by locations within the adventure
relationships:
  contained_by: "totsk.location.throne-room"

# Locations are contained by the adventure
relationships:
  contained_by: "totsk.tomb-of-the-serpent-kings"
```

## Campaign Integration Workflow

### Option 1: Manual Integration
When a user wants to incorporate a standalone adventure into a campaign:

1. **Create or identify target campaign**
2. **Update adventure's organizational hierarchy**
3. **Establish relationships with campaign entities**
4. **Update campaign to reference the adventure**

```python
def integrate_adventure_into_campaign(adventure_id, campaign_id):
    """Integrate standalone adventure into existing campaign"""
    
    # Update adventure
    adventure = load_entity(adventure_id)
    adventure['organizational_hierarchy']['campaign_id'] = campaign_id
    adventure['relationships']['contained_by'] = campaign_id
    
    # Update campaign
    campaign = load_entity(campaign_id)
    campaign['relationships']['contains'].append(adventure_id)
    campaign['contained_adventures'].append(adventure_id)
    
    save_entity(adventure)
    save_entity(campaign)
```

### Option 2: Automatic Campaign Creation
For users who want to build a campaign from multiple standalone adventures:

```python
def create_campaign_from_adventures(adventure_ids, campaign_info):
    """Create new campaign containing multiple standalone adventures"""
    
    campaign = create_campaign_entity(campaign_info)
    
    for adventure_id in adventure_ids:
        integrate_adventure_into_campaign(adventure_id, campaign['id'])
    
    return campaign
```

## Practical Examples

### Example 1: Processing "The Sunless Citadel"

**Initial Processing (Standalone)**:
```yaml
id: "sunless-citadel"
organizational_hierarchy:
  campaign_id: null
  module_id: "sunless-citadel"
relationships:
  contained_by: null
  contains: ["sc.location.entrance", "sc.npc.meepo"]
```

**Later Integration into "Tales from the Yawning Portal" Campaign**:
```yaml
id: "sunless-citadel"
organizational_hierarchy:
  campaign_id: "tales-from-yawning-portal"
  module_id: "sunless-citadel"
relationships:
  contained_by: "tales-from-yawning-portal"
  contains: ["sc.location.entrance", "sc.npc.meepo"]
  enables: ["tftyp.forge-of-fury"]  # Unlocks next adventure
```

### Example 2: One-Shot Collection

Multiple one-shots can be organized into a campaign:

```yaml
# Campaign: "Monthly One-Shots"
contained_adventures: [
  "tomb-of-the-serpent-kings",
  "tower-of-the-stargazer", 
  "prison-of-the-hated-pretender"
]

# Each adventure maintains its standalone structure
# but gains campaign context for cross-referencing
```

## Benefits for Your Pipeline

### 1. **Flexible Processing**
- Process any adventure as standalone by default
- No need to know campaign context during initial processing
- Clean, self-contained entities

### 2. **Easy Integration**
- Simple field updates to integrate into campaigns
- Relationships can be established post-processing
- No need to reprocess content

### 3. **Modular Content Management**
- Adventures remain usable independently
- Can be part of multiple campaigns if desired
- Clear separation of concerns

### 4. **AI/LLM Benefits**
- Standalone adventures have complete internal context
- Campaign integration adds broader narrative context
- Rich metadata supports both use cases

## Schema Validation

The schema supports both standalone and integrated adventures:

```json
{
  "organizational_hierarchy": {
    "campaign_id": {"type": ["string", "null"]},  // null for standalone
    "module_id": {"type": ["string", "null"]}     // required for all
  },
  "relationships": {
    "contained_by": {"type": ["string", "null"]}  // null for standalone
  }
}
```

## Best Practices

1. **Default to Standalone**: Process all adventures as standalone initially
2. **Use Clear Tags**: Tag adventures as "standalone", "one-shot", etc.
3. **Preserve Modularity**: Keep adventures self-contained even when integrated
4. **Document Integration**: Track when and how adventures are integrated
5. **Maintain Flexibility**: Allow adventures to be moved between campaigns

This approach ensures your pipeline can handle the full spectrum from simple one-shots to complex interconnected campaigns while maintaining the modular, flexible design that makes TTRPG content so versatile.
