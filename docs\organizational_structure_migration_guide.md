# Migration Guide: Implementing Enhanced Organizational Structure

## Overview

This guide helps you transition from your current schema structure to the enhanced organizational framework. The migration preserves all existing functionality while adding powerful new organizational capabilities.

## What's New

### 1. Enhanced Universal Fields
- **`organizational_hierarchy`**: Defines hierarchical position within campaigns
- **`relationships`**: Tracks connections between entities
- **`content_organization`**: Metadata for presentation and complexity

### 2. New Entity Types
- **`campaign`**: Top-level campaign containers
- **`chapter`**: Major divisions within adventures
- **`faction`**: Organizations and groups
- **`event`**: Specific occurrences or encounters
- **`quest`**: Objectives and missions
- **`organization`**: Formal groups and institutions
- **`settlement`**: Towns, cities, and communities
- **`region`**: Geographic areas

### 3. New Entity Definitions
- **`campaign_yaml`**: Complete campaign structure
- **`chapter_yaml`**: Chapter organization within adventures
- **`faction_yaml`**: Detailed faction/organization tracking

## Migration Steps

### Step 1: Update Existing Entities

#### Current Adventure Entity
```yaml
---
id: "tlom.the-light-of-memoria"
entity_type: "adventure"
name: "the-light-of-memoria"
game_system: "dnd5e"
adventure_slug: "tlom"
source_book: "The Light of Memoria"
tags: ["dungeon", "exploration", "mystery"]
summary: "An ancient temple holds secrets of a forgotten civilization."
---
```

#### Enhanced Adventure Entity
```yaml
---
id: "tlom.the-light-of-memoria"
entity_type: "adventure"
name: "the-light-of-memoria"
game_system: "dnd5e"
adventure_slug: "tlom"
organizational_hierarchy:
  campaign_id: "forgotten-realms-campaign"  # NEW
  module_id: "tlom.the-light-of-memoria"    # NEW
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "The Light of Memoria"
tags: ["dungeon", "exploration", "mystery"]
summary: "An ancient temple holds secrets of a forgotten civilization."
relationships:                              # NEW
  contains: ["tlom.location.temple-entrance", "tlom.npc.ancient-guardian"]
  contained_by: "forgotten-realms-campaign"
  references: ["tlom.item.memoria-crystal"]
  referenced_by: []
  depends_on: []
  enables: ["tlom.chapter-2-deeper-mysteries"]
content_organization:                        # NEW
  display_order: 1
  content_type: "encounter"
  complexity_level: "moderate"
  estimated_duration_minutes: 240
  prerequisites: []
# ... existing adventure-specific fields remain unchanged
---
```

### Step 2: Create Campaign Containers

For each adventure or group of related adventures, create a campaign entity:

```yaml
---
id: "forgotten-realms-campaign"
entity_type: "campaign"
name: "forgotten-realms-campaign"
game_system: "dnd5e"
adventure_slug: "fr"
organizational_hierarchy:
  campaign_id: "forgotten-realms-campaign"
  module_id: null
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Custom Campaign"
tags: ["forgotten realms", "high fantasy", "exploration"]
summary: "A campaign exploring the mysteries of the Forgotten Realms."
relationships:
  contains: ["tlom.the-light-of-memoria", "fr.other-adventure"]
  contained_by: null
  references: []
  referenced_by: []
  depends_on: []
  enables: ["tlom.the-light-of-memoria"]
content_organization:
  display_order: 1
  content_type: "narrative"
  complexity_level: "advanced"
  estimated_duration_minutes: 10800
  prerequisites: []
campaign_title: "Forgotten Realms Exploration Campaign"
campaign_theme: "High Fantasy Exploration"
target_level_range:
  start_level: 1
  end_level: 15
estimated_duration_sessions: 25
contained_adventures: ["tlom.the-light-of-memoria"]
major_npcs: ["tlom.npc.ancient-guardian"]
central_locations: ["tlom.location.temple-of-memoria"]
overarching_plot: "Heroes explore ancient mysteries across the Forgotten Realms."
---
```

### Step 3: Add Relationship Tracking

Update existing entities to include relationship information:

#### NPCs
```yaml
relationships:
  contains: []  # NPCs typically don't contain other entities
  contained_by: "tlom.location.temple-chamber-3"
  references: ["tlom.item.memoria-crystal", "tlom.npc.temple-priest"]
  referenced_by: ["tlom.adventure-section.guardian-encounter"]
  depends_on: ["tlom.event.temple-awakening"]
  enables: ["tlom.quest.restore-the-light"]
```

#### Locations
```yaml
relationships:
  contains: ["tlom.npc.ancient-guardian", "tlom.item.memoria-crystal"]
  contained_by: "tlom.the-light-of-memoria"
  references: ["tlom.location.outer-temple"]
  referenced_by: ["tlom.adventure-section.temple-exploration"]
  depends_on: []
  enables: ["tlom.location.inner-sanctum"]
```

### Step 4: Organize Content by Type

Create directory structure that reflects the new organization:

```
content/
├── campaigns/
│   └── forgotten-realms-campaign.md
├── adventures/
│   └── tlom.the-light-of-memoria.md
├── chapters/
│   ├── tlom.chapter-1-temple-approach.md
│   └── tlom.chapter-2-deeper-mysteries.md
├── adventure-sections/
│   ├── tlom.section.temple-entrance.md
│   └── tlom.section.guardian-chamber.md
├── locations/
│   ├── tlom.location.temple-entrance.md
│   └── tlom.location.inner-sanctum.md
├── npcs/
│   └── tlom.npc.ancient-guardian.md
├── factions/
│   └── tlom.faction.temple-keepers.md
└── events/
    └── tlom.event.temple-awakening.md
```

### Step 5: Update Processing Scripts

Modify your processing scripts to handle the new fields:

```python
def migrate_entity(entity_data):
    """Migrate existing entity to new structure"""
    
    # Add organizational hierarchy if missing
    if 'organizational_hierarchy' not in entity_data:
        entity_data['organizational_hierarchy'] = {
            'campaign_id': infer_campaign_id(entity_data),
            'module_id': entity_data.get('adventure_slug'),
            'chapter_id': None,
            'section_id': None,
            'subsection_id': None
        }
    
    # Add empty relationships if missing
    if 'relationships' not in entity_data:
        entity_data['relationships'] = {
            'contains': [],
            'contained_by': None,
            'references': [],
            'referenced_by': [],
            'depends_on': [],
            'enables': []
        }
    
    # Add content organization if missing
    if 'content_organization' not in entity_data:
        entity_data['content_organization'] = {
            'display_order': None,
            'content_type': infer_content_type(entity_data),
            'complexity_level': 'moderate',
            'estimated_duration_minutes': None,
            'prerequisites': []
        }
    
    return entity_data
```

## Backward Compatibility

The enhanced structure is fully backward compatible:

1. **Existing Fields**: All current fields remain unchanged
2. **Optional New Fields**: New organizational fields are optional
3. **Gradual Migration**: You can migrate entities incrementally
4. **Legacy Support**: Processing scripts can handle both old and new formats

## Benefits After Migration

1. **Enhanced Navigation**: Clear hierarchical relationships
2. **Better Cross-Referencing**: Rich relationship tracking
3. **Improved AI Processing**: Better context for LLM systems
4. **Flexible Organization**: Support for complex campaign structures
5. **Future-Proof**: Extensible framework for new content types

## Testing the Migration

1. **Validate Schemas**: Ensure all entities pass schema validation
2. **Test Relationships**: Verify relationship links are bidirectional
3. **Check Hierarchy**: Confirm organizational hierarchy is consistent
4. **Process Content**: Run through your existing pipeline
5. **Verify Output**: Ensure enhanced metadata improves RAG performance

## Next Steps

1. Start with a small subset of content for initial migration
2. Update your processing pipeline to handle new fields
3. Gradually migrate existing content
4. Create new content using the enhanced structure
5. Leverage the organizational features for improved content management
