# Nested Locations and Chapter/Location Coordination

## Overview

This document demonstrates how to handle nested locations and the common scenario where chapters and major locations share names (e.g., "Chapter 2: Village of Barovia" and "Village of Barovia" location).

## Nested Location Hierarchy

### Example: Village of Barovia Structure

```
Chapter 2: Village of Barovia
├── Village of Barovia (settlement)
│   ├── Blood on the Vine Tavern (building)
│   │   ├── Common Room (room)
│   │   ├── Private Rooms (room)
│   │   └── Cellar (room)
│   ├── Church of St. Andral (building)
│   │   ├── Nave (room)
│   │   ├── Altar (room)
│   │   └── Crypt (room)
│   ├── Burgoma<PERSON>'s House (building)
│   └── Village Square (area)
└── Events: Meeting Ireena, <PERSON><PERSON><PERSON><PERSON>'s Funeral
```

## Implementation Examples

### Chapter Entity

```yaml
---
id: "cos.chapter-2-village-of-barovia"
entity_type: "chapter"
name: "chapter-2-village-of-barovia"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.village-of-barovia"
  chapter_id: "cos.chapter-2-village-of-barovia"
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["village", "investigation", "social", "exploration"]
summary: "The characters explore the gloomy village of Barovia and meet its downtrodden inhabitants."
relationships:
  contains: [
    "cos.location.village-of-barovia",
    "cos.event.meeting-ireena", 
    "cos.event.burgomaster-funeral"
  ]
  contained_by: "cos.village-of-barovia"
  references: ["cos.npc.strahd-von-zarovich"]
  referenced_by: ["cos.chapter-3-tser-pool"]
  depends_on: ["cos.death-house"]
  enables: ["cos.chapter-3-tser-pool"]
content_organization:
  display_order: 2
  content_type: "narrative"
  complexity_level: "moderate"
  estimated_duration_minutes: 180
  prerequisites: ["cos.death-house"]
chapter_title: "Village of Barovia"
parent_adventure_id: "cos.village-of-barovia"
chapter_number: 2
chapter_objectives:
  - "Meet Ireena Kolyana and learn about Strahd's interest in her"
  - "Explore the village and understand the plight of its inhabitants"
  - "Gather information about the surrounding areas and potential allies"
contained_sections: ["cos.section.village-approach", "cos.section.village-exploration"]
key_events: ["cos.event.meeting-ireena", "cos.event.burgomaster-funeral"]
chapter_summary: "The characters arrive in the Village of Barovia, a gloomy settlement where they meet Ireena Kolyana and learn about Strahd's curse upon the land."
primary_location_id: "cos.location.village-of-barovia"
---
```

### Top-Level Location (Village)

```yaml
---
id: "cos.location.village-of-barovia"
entity_type: "location"
name: "village-of-barovia"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.village-of-barovia"
  chapter_id: "cos.chapter-2-village-of-barovia"
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["village", "settlement", "barovia", "cursed"]
summary: "A gloomy village trapped in the cursed land of Barovia, home to downtrodden souls."
relationships:
  contains: [
    "cos.location.blood-on-vine-tavern",
    "cos.location.church-of-st-andral", 
    "cos.location.burgomaster-house",
    "cos.location.village-square"
  ]
  contained_by: "cos.chapter-2-village-of-barovia"
  references: ["cos.npc.ireena-kolyana", "cos.npc.ismark-kolyanovich"]
  referenced_by: ["cos.chapter-3-tser-pool"]
  depends_on: []
  enables: ["cos.location.church-of-st-andral"]
content_organization:
  display_order: 1
  content_type: "narrative"
  complexity_level: "moderate"
  estimated_duration_minutes: null
  prerequisites: []
parent_adventure_id: "cos.village-of-barovia"
parent_section_id: null
parent_chapter_id: "cos.chapter-2-village-of-barovia"
parent_location_id: null
location_type: "settlement"
location_scale: "large"
primary_chapter_id: "cos.chapter-2-village-of-barovia"
location_type_narrative: "cursed village"
description_general_narrative: "The village of Barovia is a collection of humble wooden houses with thatched roofs, arranged around a central square. Tall, dark trees loom over the settlement, and a perpetual mist hangs in the air."
atmosphere_mood_narrative: "The village feels oppressive and melancholy. Windows are shuttered, few people walk the muddy streets, and those who do move quickly with downcast eyes. The air smells of damp earth and decay."
---
```

### Nested Location (Building)

```yaml
---
id: "cos.location.blood-on-vine-tavern"
entity_type: "location"
name: "blood-on-vine-tavern"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.village-of-barovia"
  chapter_id: "cos.chapter-2-village-of-barovia"
  section_id: "cos.section.tavern-investigation"
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["tavern", "building", "social", "investigation"]
summary: "The village's only tavern, a gloomy establishment where locals gather to drink away their sorrows."
relationships:
  contains: [
    "cos.location.tavern-common-room",
    "cos.location.tavern-private-rooms",
    "cos.location.tavern-cellar"
  ]
  contained_by: "cos.location.village-of-barovia"
  references: ["cos.npc.ismark-kolyanovich", "cos.npc.arik-lorditsky"]
  referenced_by: ["cos.event.meeting-ireena"]
  depends_on: []
  enables: ["cos.location.tavern-common-room"]
content_organization:
  display_order: 2
  content_type: "narrative"
  complexity_level: "simple"
  estimated_duration_minutes: 45
  prerequisites: []
parent_adventure_id: "cos.village-of-barovia"
parent_section_id: "cos.section.tavern-investigation"
parent_chapter_id: "cos.chapter-2-village-of-barovia"
parent_location_id: "cos.location.village-of-barovia"
location_type: "building"
location_scale: "medium"
primary_chapter_id: "cos.chapter-2-village-of-barovia"
location_type_narrative: "gloomy tavern"
description_general_narrative: "A two-story wooden building with a sagging roof and grimy windows. A weathered sign depicting a wine bottle dripping red hangs above the door."
atmosphere_mood_narrative: "The tavern is dimly lit and filled with the smell of stale ale and unwashed bodies. Patrons speak in hushed tones, and the atmosphere is heavy with despair."
---
```

### Deeply Nested Location (Room)

```yaml
---
id: "cos.location.tavern-common-room"
entity_type: "location"
name: "tavern-common-room"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.village-of-barovia"
  chapter_id: "cos.chapter-2-village-of-barovia"
  section_id: "cos.section.tavern-investigation"
  subsection_id: "cos.subsection.common-room-encounter"
source_book: "Curse of Strahd"
tags: ["room", "social", "encounter"]
summary: "The main gathering area of the Blood on the Vine tavern."
relationships:
  contains: ["cos.npc.tavern-patrons"]
  contained_by: "cos.location.blood-on-vine-tavern"
  references: ["cos.npc.ismark-kolyanovich"]
  referenced_by: []
  depends_on: []
  enables: []
content_organization:
  display_order: 1
  content_type: "encounter"
  complexity_level: "simple"
  estimated_duration_minutes: 30
  prerequisites: []
parent_adventure_id: "cos.village-of-barovia"
parent_section_id: "cos.section.tavern-investigation"
parent_chapter_id: "cos.chapter-2-village-of-barovia"
parent_location_id: "cos.location.blood-on-vine-tavern"
location_type: "room"
location_scale: "medium"
primary_chapter_id: "cos.chapter-2-village-of-barovia"
location_type_narrative: "tavern common room"
description_general_narrative: "A large room with low wooden beams, filled with rough tables and chairs. A fireplace dominates one wall, though it provides little warmth."
atmosphere_mood_narrative: "The room is poorly lit by flickering candles. A few locals sit hunched over their drinks, speaking in whispers. The floorboards creak ominously underfoot."
---
```

## Key Benefits of This Approach

### 1. **Clear Hierarchy**
- Each location knows its parent through `parent_location_id`
- Each location knows what it contains through `relationships.contains`
- Scale and type provide additional context

### 2. **Chapter/Location Coordination**
- Chapters can reference their primary location via `primary_location_id`
- Locations can reference their primary chapter via `primary_chapter_id`
- Bidirectional relationship maintains consistency

### 3. **Flexible Navigation**
- Can traverse up the hierarchy: Room → Building → Settlement → Chapter
- Can traverse down the hierarchy: Chapter → Settlement → Buildings → Rooms
- Can jump between related entities via relationships

### 4. **Pipeline Processing**
- Clear organizational hierarchy helps with automated extraction
- Relationship tracking enables intelligent cross-referencing
- Scale and type information aids in content organization

### 5. **Reassembly Support**
- Hierarchical structure makes it easy to reconstruct the adventure
- Relationships provide context for how pieces fit together
- Chapter/location coordination maintains narrative flow

## File Organization Example

```
content/
├── chapters/
│   └── cos.chapter-2-village-of-barovia.md
├── locations/
│   ├── cos.location.village-of-barovia.md
│   ├── cos.location.blood-on-vine-tavern.md
│   ├── cos.location.tavern-common-room.md
│   ├── cos.location.church-of-st-andral.md
│   └── cos.location.burgomaster-house.md
├── npcs/
│   ├── cos.npc.ireena-kolyana.md
│   └── cos.npc.ismark-kolyanovich.md
└── events/
    ├── cos.event.meeting-ireena.md
    └── cos.event.burgomaster-funeral.md
```

This structure provides the organizational benefits you need while maintaining the simplicity required for your pipeline to effectively extract, process, and reassemble adventure content.
