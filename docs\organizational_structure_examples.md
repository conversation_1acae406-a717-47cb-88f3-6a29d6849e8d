# Organizational Structure Implementation Examples

## Example 1: Campaign Entity

```yaml
---
id: "curse-of-strahd-campaign"
entity_type: "campaign"
name: "curse-of-strahd"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: null
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["gothic horror", "undead", "vampire", "ravenloft"]
aliases: ["CoS", "Strahd Campaign"]
summary: "A gothic horror campaign where heroes must defeat the vampire lord <PERSON><PERSON><PERSON> in the cursed land of Barovia."
relationships:
  contains: ["cos.death-house", "cos.village-of-barovia", "cos.castle-ravenloft", "cos.npc.strahd-von-zarovich"]
  contained_by: null
  references: ["cos.item.sunsword", "cos.item.holy-symbol-of-ravenkind"]
  referenced_by: []
  depends_on: []
  enables: ["cos.death-house"]
content_organization:
  display_order: 1
  content_type: "narrative"
  complexity_level: "advanced"
  estimated_duration_minutes: 18000
  prerequisites: []
campaign_title: "Curse of Strahd"
campaign_theme: "Gothic Horror"
target_level_range:
  start_level: 1
  end_level: 10
estimated_duration_sessions: 30
contained_adventures: ["cos.death-house", "cos.village-of-barovia", "cos.castle-ravenloft"]
major_npcs: ["cos.npc.strahd-von-zarovich", "cos.npc.ireena-kolyana", "cos.npc.ismark-kolyanovich"]
central_locations: ["cos.location.village-of-barovia", "cos.location.castle-ravenloft", "cos.location.death-house"]
overarching_plot: "The heroes are drawn into the cursed realm of Barovia, ruled by the vampire lord Strahd von Zarovich. They must gather allies, find powerful artifacts, and ultimately confront Strahd to free the land from his eternal curse."
---

# Curse of Strahd Campaign

A gothic horror campaign set in the cursed land of Barovia...
```

## Example 2: Adventure Entity

```yaml
---
id: "cos.death-house"
entity_type: "adventure"
name: "death-house"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.death-house"
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["haunted house", "undead", "investigation", "horror"]
aliases: ["Durst House", "The Death House"]
summary: "A haunted mansion that serves as an introduction to the horrors of Barovia."
relationships:
  contains: ["cos.location.death-house-entrance", "cos.npc.rose-durst", "cos.npc.thorn-durst"]
  contained_by: "curse-of-strahd-campaign"
  references: ["cos.npc.strahd-von-zarovich"]
  referenced_by: ["cos.village-of-barovia"]
  depends_on: []
  enables: ["cos.village-of-barovia"]
content_organization:
  display_order: 1
  content_type: "encounter"
  complexity_level: "moderate"
  estimated_duration_minutes: 240
  prerequisites: []
adventure_title: "Death House"
tagline: "A haunted introduction to the horrors of Barovia"
intended_pc_level_narrative: "1st to 3rd level characters"
authors: ["Chris Perkins"]
setting_overview: "A seemingly abandoned mansion on the outskirts of the Village of Barovia"
plot_hook: "The characters encounter two ghostly children who plead for help finding their parents"
plot_summary_narrative: "The characters explore a haunted house, uncovering the tragic history of the Durst family and confronting the shambling mound in the basement"
key_factions_narrative:
  - name: "Durst Family Ghosts"
    description: "The restless spirits of the Durst family"
    goals_narrative: "Seeking peace and resolution to their tragic deaths"
    linked_faction_id: null
major_themes: ["family tragedy", "corruption", "sacrifice", "undeath"]
---

# Death House

The mists of Ravenloft part to reveal a tall townhouse of brick and timber...
```

## Example 3: Chapter Entity

```yaml
---
id: "cos.chapter-2-village-of-barovia"
entity_type: "chapter"
name: "chapter-2-village-of-barovia"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: "cos.village-of-barovia"
  chapter_id: "cos.chapter-2-village-of-barovia"
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["village", "investigation", "social", "exploration"]
aliases: ["Village of Barovia", "Chapter 2"]
summary: "The characters explore the gloomy village of Barovia and meet its downtrodden inhabitants."
relationships:
  contains: ["cos.location.blood-on-the-vine-tavern", "cos.location.church-of-st-andral", "cos.npc.ireena-kolyana"]
  contained_by: "cos.village-of-barovia"
  references: ["cos.npc.strahd-von-zarovich", "cos.death-house"]
  referenced_by: ["cos.chapter-3-tser-pool"]
  depends_on: ["cos.death-house"]
  enables: ["cos.chapter-3-tser-pool"]
content_organization:
  display_order: 2
  content_type: "narrative"
  complexity_level: "moderate"
  estimated_duration_minutes: 180
  prerequisites: ["cos.death-house"]
chapter_title: "Village of Barovia"
parent_adventure_id: "cos.village-of-barovia"
chapter_number: 2
chapter_objectives:
  - "Meet Ireena Kolyana and learn about Strahd's interest in her"
  - "Explore the village and understand the plight of its inhabitants"
  - "Gather information about the surrounding areas and potential allies"
contained_sections: ["cos.section.village-approach", "cos.section.village-exploration"]
key_events: ["cos.event.meeting-ireena", "cos.event.burgomaster-funeral"]
chapter_summary: "The characters arrive in the Village of Barovia, a gloomy settlement where they meet Ireena Kolyana and learn about Strahd's curse upon the land."
---

# Chapter 2: Village of Barovia

The road gradually disappears and is replaced by a twisted, muddy path...
```

## Example 4: Faction Entity

```yaml
---
id: "cos.faction.order-of-the-silver-dragon"
entity_type: "faction"
name: "order-of-the-silver-dragon"
game_system: "dnd5e"
adventure_slug: "cos"
organizational_hierarchy:
  campaign_id: "curse-of-strahd-campaign"
  module_id: null
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Curse of Strahd"
tags: ["knights", "good", "undead hunters", "silver dragon"]
aliases: ["Silver Dragon Knights", "The Order"]
summary: "A knightly order dedicated to fighting undead and protecting the innocent."
relationships:
  contains: ["cos.npc.sir-godfrey-gwilym", "cos.npc.vladimir-horngaard"]
  contained_by: "curse-of-strahd-campaign"
  references: ["cos.location.argynvostholt"]
  referenced_by: ["cos.npc.sir-godfrey-gwilym"]
  depends_on: []
  enables: ["cos.quest.restore-argynvost"]
content_organization:
  display_order: 3
  content_type: "background"
  complexity_level: "moderate"
  estimated_duration_minutes: null
  prerequisites: []
faction_name: "Order of the Silver Dragon"
faction_type: "military"
power_level: "regional"
primary_goals:
  - "Protect the innocent from undead threats"
  - "Restore honor to their fallen order"
  - "Find peace for their restless spirits"
key_members:
  - npc_id: "cos.npc.sir-godfrey-gwilym"
    role: "Revenant Knight"
    importance: "leader"
  - npc_id: "cos.npc.vladimir-horngaard"
    role: "Fallen Commander"
    importance: "leader"
allied_factions: []
enemy_factions: ["cos.faction.strahd-forces"]
resources_and_assets:
  - "Argynvostholt (ruined stronghold)"
  - "Ancient weapons and armor"
  - "Knowledge of undead weaknesses"
territory_controlled: ["cos.location.argynvostholt"]
---

# Order of the Silver Dragon

Once a proud knightly order dedicated to protecting the realm from undead threats...
```

## File Organization Structure

```
campaign-content/
├── campaigns/
│   └── curse-of-strahd-campaign.md
├── adventures/
│   ├── cos.death-house.md
│   ├── cos.village-of-barovia.md
│   └── cos.castle-ravenloft.md
├── chapters/
│   ├── cos.chapter-1-into-the-mists.md
│   ├── cos.chapter-2-village-of-barovia.md
│   └── cos.chapter-3-tser-pool.md
├── locations/
│   ├── cos.location.death-house.md
│   ├── cos.location.village-of-barovia.md
│   └── cos.location.castle-ravenloft.md
├── npcs/
│   ├── cos.npc.strahd-von-zarovich.md
│   ├── cos.npc.ireena-kolyana.md
│   └── cos.npc.ismark-kolyanovich.md
├── factions/
│   ├── cos.faction.order-of-the-silver-dragon.md
│   └── cos.faction.strahd-forces.md
└── events/
    ├── cos.event.meeting-ireena.md
    └── cos.event.burgomaster-funeral.md
```

## Single Adventure/One-Shot Integration

### Standalone Adventure Example

```yaml
---
id: "tomb-of-the-serpent-kings"
entity_type: "adventure"
name: "tomb-of-the-serpent-kings"
game_system: "dnd5e"
adventure_slug: "totsk"
organizational_hierarchy:
  campaign_id: null                    # Standalone - no parent campaign
  module_id: "tomb-of-the-serpent-kings"
  chapter_id: null
  section_id: null
  subsection_id: null
source_book: "Tomb of the Serpent Kings"
tags: ["dungeon crawl", "one-shot", "beginner", "standalone"]
summary: "A classic dungeon crawl perfect for new players and one-shot sessions."
relationships:
  contains: ["totsk.location.entrance-hall", "totsk.npc.serpent-guardian"]
  contained_by: null                   # No parent container
  references: []
  referenced_by: []
  depends_on: []                       # No dependencies
  enables: []                          # Doesn't unlock other content
content_organization:
  display_order: 1
  content_type: "encounter"
  complexity_level: "simple"
  estimated_duration_minutes: 240     # 4-hour one-shot
  prerequisites: []                    # No prerequisites needed
adventure_title: "Tomb of the Serpent Kings"
tagline: "A deadly dungeon for brave adventurers"
intended_pc_level_narrative: "1st to 3rd level characters"
authors: ["Skerples"]
setting_overview: "An ancient tomb filled with traps, monsters, and treasure"
plot_hook: "Rumors speak of a great treasure hidden in an ancient tomb"
plot_summary_narrative: "Heroes explore a dangerous dungeon, learning the basics of careful exploration and tactical combat"
major_themes: ["exploration", "danger", "treasure hunting", "learning"]
---
```

### Later Campaign Integration

When incorporating into a campaign, simply update:

```yaml
organizational_hierarchy:
  campaign_id: "my-homebrew-campaign"  # Now part of a campaign
  module_id: "tomb-of-the-serpent-kings"
  chapter_id: null
  section_id: null
  subsection_id: null
relationships:
  contains: ["totsk.location.entrance-hall", "totsk.npc.serpent-guardian"]
  contained_by: "my-homebrew-campaign"  # Now contained by campaign
  references: ["campaign.npc.quest-giver"]  # Can reference campaign NPCs
  referenced_by: ["campaign.quest.find-ancient-artifacts"]
  depends_on: ["campaign.event.village-attack"]  # Can depend on campaign events
  enables: ["campaign.location.hidden-valley"]   # Can unlock campaign content
```

This organizational structure provides:
- **Standalone Flexibility**: Adventures work independently
- **Campaign Integration**: Easy incorporation into larger narratives
- **Modular Design**: Content can be mixed and matched
- **Clear Relationships**: Explicit connections when integrated
- **Scalable Organization**: From one-shots to epic campaigns
- **AI/LLM Optimization**: Rich context for content generation
